{% extends "base.html" %}

{% block title %}Search Properties - Real Home{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Search Filters Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="search-filters sticky-top">
                <h5 class="mb-3">Filter Properties</h5>
                
                <form method="GET" action="/search" id="searchForm">
                    <!-- Location -->
                    <div class="filter-section">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location" 
                               value="{{ search_params.location or '' }}" 
                               placeholder="City, State, or ZIP">
                    </div>
                    
                    <!-- Price Range -->
                    <div class="filter-section">
                        <label class="form-label">Price Range</label>
                        <div class="row">
                            <div class="col-6">
                                <select class="form-select form-select-sm" name="min_price">
                                    <option value="">Min Price</option>
                                    <option value="100000" {% if search_params.min_price == 100000 %}selected{% endif %}>$100K</option>
                                    <option value="200000" {% if search_params.min_price == 200000 %}selected{% endif %}>$200K</option>
                                    <option value="300000" {% if search_params.min_price == 300000 %}selected{% endif %}>$300K</option>
                                    <option value="400000" {% if search_params.min_price == 400000 %}selected{% endif %}>$400K</option>
                                    <option value="500000" {% if search_params.min_price == 500000 %}selected{% endif %}>$500K</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <select class="form-select form-select-sm" name="max_price">
                                    <option value="">Max Price</option>
                                    <option value="300000" {% if search_params.max_price == 300000 %}selected{% endif %}>$300K</option>
                                    <option value="500000" {% if search_params.max_price == 500000 %}selected{% endif %}>$500K</option>
                                    <option value="750000" {% if search_params.max_price == 750000 %}selected{% endif %}>$750K</option>
                                    <option value="1000000" {% if search_params.max_price == 1000000 %}selected{% endif %}>$1M</option>
                                    <option value="1500000" {% if search_params.max_price == 1500000 %}selected{% endif %}>$1.5M</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bedrooms & Bathrooms -->
                    <div class="filter-section">
                        <div class="row">
                            <div class="col-6">
                                <label class="form-label">Bedrooms</label>
                                <select class="form-select form-select-sm" name="bedrooms">
                                    <option value="">Any</option>
                                    <option value="1" {% if search_params.bedrooms == 1 %}selected{% endif %}>1+</option>
                                    <option value="2" {% if search_params.bedrooms == 2 %}selected{% endif %}>2+</option>
                                    <option value="3" {% if search_params.bedrooms == 3 %}selected{% endif %}>3+</option>
                                    <option value="4" {% if search_params.bedrooms == 4 %}selected{% endif %}>4+</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label">Bathrooms</label>
                                <select class="form-select form-select-sm" name="bathrooms">
                                    <option value="">Any</option>
                                    <option value="1" {% if search_params.bathrooms == 1 %}selected{% endif %}>1+</option>
                                    <option value="2" {% if search_params.bathrooms == 2 %}selected{% endif %}>2+</option>
                                    <option value="3" {% if search_params.bathrooms == 3 %}selected{% endif %}>3+</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Property Type -->
                    <div class="filter-section">
                        <label class="form-label">Property Type</label>
                        <select class="form-select form-select-sm" name="property_type">
                            <option value="">All Types</option>
                            <option value="house" {% if search_params.property_type == 'house' %}selected{% endif %}>House</option>
                            <option value="condo" {% if search_params.property_type == 'condo' %}selected{% endif %}>Condo</option>
                            <option value="townhouse" {% if search_params.property_type == 'townhouse' %}selected{% endif %}>Townhouse</option>
                            <option value="apartment" {% if search_params.property_type == 'apartment' %}selected{% endif %}>Apartment</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mt-3">
                        <i class="fas fa-search me-2"></i>Update Search
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Search Results -->
        <div class="col-lg-9">
            <!-- Results Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4>Search Results</h4>
                    <p class="text-muted mb-0">{{ total }} properties found</p>
                </div>
                
                <div class="d-flex align-items-center">
                    <label for="sort_by" class="form-label me-2 mb-0">Sort by:</label>
                    <select class="form-select form-select-sm" name="sort_by" id="sort_by" style="width: auto;" onchange="updateSort()">
                        <option value="price_asc" {% if search_params.sort_by == 'price_asc' %}selected{% endif %}>Price: Low to High</option>
                        <option value="price_desc" {% if search_params.sort_by == 'price_desc' %}selected{% endif %}>Price: High to Low</option>
                        <option value="date_desc" {% if search_params.sort_by == 'date_desc' %}selected{% endif %}>Newest First</option>
                    </select>
                </div>
            </div>
            
            <!-- Property Grid -->
            {% if properties %}
            <div class="row">
                {% for property in properties %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card property-card h-100">
                        <div class="position-relative">
                            {% if property.images %}
                                <img src="{{ property.images[0].image_url }}" class="card-img-top property-image" 
                                     alt="{{ property.title }}">
                            {% else %}
                                <div class="card-img-top property-image bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-home fa-3x text-muted"></i>
                                </div>
                            {% endif %}
                            <div class="position-absolute top-0 start-0 m-2">
                                <span class="badge bg-primary fs-6">${{ "{:,}".format(property.price) }}</span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <h6 class="card-title">{{ property.title }}</h6>
                            <p class="card-text text-muted small">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ property.city }}, {{ property.state }}
                            </p>
                            
                            <div class="property-details mb-3">
                                <small class="me-2">
                                    <i class="fas fa-bed me-1"></i>{{ property.bedrooms }}
                                </small>
                                <small class="me-2">
                                    <i class="fas fa-bath me-1"></i>{{ property.bathrooms }}
                                </small>
                                {% if property.square_feet %}
                                <small>
                                    <i class="fas fa-ruler-combined me-1"></i>{{ "{:,}".format(property.square_feet) }} sqft
                                </small>
                                {% endif %}
                            </div>
                            
                            <a href="/property/{{ property.id }}" class="btn btn-outline-primary btn-sm">View Details</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if total_pages > 1 %}
            <nav aria-label="Search results pagination">
                <ul class="pagination justify-content-center">
                    {% if has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ prev_page }}{% for key, value in request.query_params.items() %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% set start_page = [1, page - 2]|max %}
                    {% set end_page = [total_pages, page + 2]|min %}

                    {% for page_num in range(start_page, end_page + 1) %}
                        {% if page_num == page %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_num }}{% for key, value in request.query_params.items() %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ next_page }}{% for key, value in request.query_params.items() %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>No properties found</h5>
                <p class="text-muted">Try adjusting your search criteria to find more properties.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function updateSort() {
    const form = document.getElementById('searchForm');
    const sortSelect = document.getElementById('sort_by');
    
    // Add sort_by to form
    const sortInput = document.createElement('input');
    sortInput.type = 'hidden';
    sortInput.name = 'sort_by';
    sortInput.value = sortSelect.value;
    form.appendChild(sortInput);
    
    form.submit();
}
</script>
{% endblock %}
