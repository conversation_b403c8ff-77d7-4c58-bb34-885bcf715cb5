{% extends "base.html" %}

{% block title %}Real Home - Find Your Dream Home{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section text-white">
    <div class="container-fluid">
        <div class="container">
            <div class="hero-content">
                <div class="hero-left">
                    <h1 class="hero-title">Find Your Dream Home</h1>
                    <p class="hero-subtitle">Discover the perfect property with our comprehensive search tools and expert guidance.</p>
                </div>
                <div class="hero-right">
                    <div class="search-card">
                        <form action="/search" method="GET">
                            <div class="mb-3">
                                <label for="location" class="form-label text-dark">Location</label>
                                <input type="text" class="form-control" id="location" name="location"
                                       placeholder="Enter city, state, or ZIP code">
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <label for="min_price" class="form-label text-dark">Min Price</label>
                                    <select class="form-select" id="min_price" name="min_price">
                                        <option value="">No Min</option>
                                        <option value="100000">$100,000</option>
                                        <option value="200000">$200,000</option>
                                        <option value="300000">$300,000</option>
                                        <option value="400000">$400,000</option>
                                        <option value="500000">$500,000</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <label for="max_price" class="form-label text-dark">Max Price</label>
                                    <select class="form-select" id="max_price" name="max_price">
                                        <option value="">No Max</option>
                                        <option value="300000">$300,000</option>
                                        <option value="400000">$400,000</option>
                                        <option value="500000">$500,000</option>
                                        <option value="750000">$750,000</option>
                                        <option value="1000000">$1,000,000</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <label for="bedrooms" class="form-label text-dark">Bedrooms</label>
                                    <select class="form-select" id="bedrooms" name="bedrooms">
                                        <option value="">Any</option>
                                        <option value="1">1+</option>
                                        <option value="2">2+</option>
                                        <option value="3">3+</option>
                                        <option value="4">4+</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <label for="property_type" class="form-label text-dark">Property Type</label>
                                    <select class="form-select" id="property_type" name="property_type">
                                        <option value="">All Types</option>
                                        <option value="house">House</option>
                                        <option value="condo">Condo</option>
                                        <option value="townhouse">Townhouse</option>
                                        <option value="apartment">Apartment</option>
                                    </select>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Search Properties
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Properties -->
<section class="featured-properties">
    <div class="container">
        <h2 class="text-center">Featured Properties</h2>
        
        {% if featured_properties %}
        <div class="row g-4">
            {% for property in featured_properties %}
            <div class="col-lg-4 col-md-6">
                <div class="card property-card shadow-sm">
                    <div class="position-relative">
                        {% if property.images %}
                            <img src="{{ property.images[0].image_url }}" class="card-img-top property-image" 
                                 alt="{{ property.title }}">
                        {% else %}
                            <div class="card-img-top property-image bg-light d-flex align-items-center justify-content-center">
                                <i class="fas fa-home fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        <div class="position-absolute top-0 start-0 m-3">
                            <span class="badge bg-primary fs-6">${{ "{:,}".format(property.price) }}</span>
                        </div>
                    </div>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ property.title }}</h5>
                        <p class="card-text text-muted">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ property.city }}, {{ property.state }}
                        </p>

                        <div class="property-details mb-3">
                            <span>
                                <i class="fas fa-bed"></i>{{ property.bedrooms }}
                            </span>
                            <span>
                                <i class="fas fa-bath"></i>{{ property.bathrooms }}
                            </span>
                            {% if property.square_feet %}
                            <span>
                                <i class="fas fa-ruler-combined"></i>{{ "{:,}".format(property.square_feet) }} sqft
                            </span>
                            {% endif %}
                        </div>

                        <div class="mt-auto">
                            <a href="/property/{{ property.id }}" class="btn btn-outline-primary w-100">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-4">
            <a href="/search" class="btn btn-primary btn-lg">View All Properties</a>
        </div>
        {% else %}
        <div class="text-center">
            <p class="text-muted">No properties available at the moment.</p>
            <a href="/search" class="btn btn-primary">Browse Properties</a>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}
