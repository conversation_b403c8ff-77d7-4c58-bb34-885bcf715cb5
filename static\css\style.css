/* Custom CSS for Real Home - Zillow Clone */

:root {
    --primary-color: #006ba6;
    --secondary-color: #0496c7;
    --accent-color: #9bb1ff;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --border-color: #e9ecef;
    --hover-color: #f8f9fa;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Navigation */
.navbar {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
    background-color: white !important;
    backdrop-filter: blur(10px);
    z-index: 1030;
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.navbar-brand:hover {
    color: var(--secondary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem !important;
    margin: 0 0.25rem;
    border-radius: 6px;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--hover-color);
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    min-height: 500px;
    padding-top: 2rem;
    padding-bottom: 4rem;
}

.search-card {
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

/* Property Cards */
.property-card {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    height: 100%;
}

.property-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border-color: var(--primary-color);
}

.property-image {
    height: 220px;
    object-fit: cover;
    width: 100%;
    background-color: var(--hover-color);
}

.property-details {
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
}

.property-details span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.property-details i {
    color: var(--primary-color);
    font-size: 0.8rem;
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    color: var(--text-dark);
}

/* Search Page */
.search-filters {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filter-section {
    margin-bottom: 1rem;
}

.filter-section:last-child {
    margin-bottom: 0;
}

/* Property Detail Page */
.property-gallery {
    border-radius: 12px;
    overflow: hidden;
}

.property-main-image {
    height: 400px;
    object-fit: cover;
    width: 100%;
}

.property-thumbnail {
    height: 80px;
    object-fit: cover;
    width: 100%;
    cursor: pointer;
    border-radius: 8px;
    transition: opacity 0.3s ease;
}

.property-thumbnail:hover {
    opacity: 0.8;
}

.property-info-card {
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.property-price {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.property-features {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--hover-color);
    border-radius: 20px;
    font-size: 0.9rem;
}

.feature-item i {
    color: var(--primary-color);
}

/* Map */
.map-container {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

/* Forms */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 107, 166, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
    padding: 0.75rem 1rem;
}

.pagination .page-link:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Layout Improvements */
.container {
    max-width: 1200px;
}

.row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.row > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Featured Properties Section */
.featured-properties {
    padding: 4rem 0;
}

.featured-properties h2 {
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 3rem;
}

/* Price Badge */
.badge {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
}

/* Address styling */
.card-text {
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: auto;
        padding: 3rem 0;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .property-features {
        justify-content: center;
        gap: 0.5rem;
    }

    .search-filters {
        padding: 1rem;
    }

    .property-image {
        height: 200px;
    }

    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-section .row {
        flex-direction: column-reverse;
    }

    .search-card {
        margin-top: 2rem;
    }

    .property-details {
        font-size: 0.8rem;
        gap: 0.5rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}
