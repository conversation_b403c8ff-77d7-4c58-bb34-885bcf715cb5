from typing import List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from database import get_db
import schemas
import crud
import auth

router = APIRouter(prefix="/api/properties", tags=["properties"])

@router.get("/", response_model=List[schemas.Property])
def get_properties(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    properties = crud.get_properties(db, skip=skip, limit=limit)
    return properties

@router.get("/search")
def search_properties(
    location: str = Query(None, description="Search by city, state, or address"),
    min_price: int = Query(None, description="Minimum price"),
    max_price: int = Query(None, description="Maximum price"),
    bedrooms: int = Query(None, description="Minimum number of bedrooms"),
    bathrooms: float = Query(None, description="Minimum number of bathrooms"),
    property_type: str = Query(None, description="Type of property"),
    listing_type: str = Query("for_sale", description="Listing type"),
    sort_by: str = Query("price_asc", description="Sort order"),
    page: int = Query(1, description="Page number"),
    limit: int = Query(20, description="Items per page"),
    db: Session = Depends(get_db)
):
    search_params = schemas.PropertySearch(
        location=location,
        min_price=min_price,
        max_price=max_price,
        bedrooms=bedrooms,
        bathrooms=bathrooms,
        property_type=property_type,
        listing_type=listing_type,
        sort_by=sort_by,
        page=page,
        limit=limit
    )
    
    properties, total = crud.search_properties(db, search_params)
    
    return {
        "properties": properties,
        "total": total,
        "page": page,
        "limit": limit,
        "total_pages": (total + limit - 1) // limit
    }

@router.get("/{property_id}", response_model=schemas.Property)
def get_property(property_id: int, db: Session = Depends(get_db)):
    property = crud.get_property(db, property_id=property_id)
    if property is None:
        raise HTTPException(status_code=404, detail="Property not found")
    return property

@router.post("/", response_model=schemas.Property)
def create_property(
    property: schemas.PropertyCreate,
    current_user: schemas.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    return crud.create_property(db=db, property=property, agent_id=current_user.id)

@router.post("/{property_id}/images", response_model=schemas.PropertyImage)
def add_property_image(
    property_id: int,
    image: schemas.PropertyImageCreate,
    current_user: schemas.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    # Verify property exists and user owns it
    property = crud.get_property(db, property_id=property_id)
    if not property:
        raise HTTPException(status_code=404, detail="Property not found")
    
    if property.agent_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this property")
    
    return crud.create_property_image(db=db, image=image, property_id=property_id)
